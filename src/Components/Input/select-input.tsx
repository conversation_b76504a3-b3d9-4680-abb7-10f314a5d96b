'use client';

import FileCopyIcon from '@mui/icons-material/FileCopy';
import ReportProblemOutlinedIcon from '@mui/icons-material/ReportProblemOutlined';
import { Box, FormControl, FormHelperText, IconButton, MenuItem, Select, type SelectProps, Tooltip } from '@mui/material';
import InputLabel from 'Components/InputLabel';
import type React from 'react';
import { memo, useCallback, useState } from 'react';

interface AnyErrorObj {
  [key: string]: any;
}

export interface SelectOption {
  value: string;
  label: string;
}

export type SelectInputProps = Omit<SelectProps, 'onChange'> & {
  id: string;
  label?: string;
  copyEnabled?: boolean;
  errorMessage?: string | AnyErrorObj;
  startAdornment?: React.ReactNode;
  endAdornment?: React.ReactNode;
  onChange?: (val: string) => void;
  helperText?: string;
  options: SelectOption[];
  placeholder?: string;
  labelSx?: object;
  inputSx?: object;
};

const getEndAdornment = ({
  copyEnabled,
  value,
  isError,
  endAdornment,
}: {
  copyEnabled: boolean;
  value?: SelectProps['value'];
  isError: boolean | undefined;
  endAdornment: React.ReactNode;
}) => {
  if (endAdornment && !isError) return endAdornment;
  if (isError) return <ReportProblemOutlinedIcon color="error" />;
  if (copyEnabled)
    return (
      <Tooltip title="Copy to clipboard">
        <IconButton sx={{ cursor: 'pointer' }}>
          <FileCopyIcon />
        </IconButton>
      </Tooltip>
    );
};

const SelectInput: React.FC<SelectInputProps> = ({
  id,
  value,
  label,
  helperText,
  disabled = false,
  endAdornment,
  copyEnabled = false,
  errorMessage,
  onChange,
  options,
  placeholder,
  labelSx,
  inputSx,
  ...rest
}) => {
  const isError = !!errorMessage;
  const [isOpen, setIsOpen] = useState(false);

  const handleChangeEvent = useCallback(
    (e: any) => {
      if (onChange) onChange(e?.target?.value);
    },
    [onChange],
  );

  const handleOpen = () => {
    setIsOpen(true);
  };

  const handleClose = () => {
    setIsOpen(false);
  };

  return (
    <Box
      sx={{
        position: 'relative',
        transition: 'min-height 0.3s ease-in-out',
        zIndex: isOpen ? 1300 : 'auto', // Ensure dropdown appears above other elements
      }}
    >
      <FormControl sx={{ width: 1 }} data-testid="selectFormControl" error={isError} disabled={disabled}>
        {label && (
          <InputLabel
            htmlFor={id}
            sx={{
              ...labelSx,
              color: 'black', // default
              '&.Mui-focused': {
                color: 'black', // keep gray on focus
              },
              '&.MuiFormLabel-root': {
                color: 'black', // ensure gray in all other states
              },
              fontSize: 17,
            }}
          >
            {label}
          </InputLabel>
        )}
        <Select
          disabled={disabled}
          data-testid="select"
          value={value || ''}
          id={id}
          displayEmpty
          open={isOpen}
          onOpen={handleOpen}
          onClose={handleClose}
          sx={{
            height: '2.75rem',
            // marginTop: 2,
            '& .MuiOutlinedInput-notchedOutline': {
              borderColor: 'gray', // default
            },
            '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
              borderColor: 'gray', // on focus
            },
            ...inputSx,
          }}
          MenuProps={{
            PaperProps: {
              sx: {
                maxHeight: 280,
                width: 'auto',
                minWidth: '100%', // Match the select input width
                maxWidth: '25rem', // Prevent it from getting too wide
              },
            },
            anchorOrigin: {
              vertical: 'top',
              horizontal: 'left',
            },
            transformOrigin: {
              vertical: 'bottom',
              horizontal: 'left',
            },
            disablePortal: true, // Keep dropdown within the local container
          }}
          onChange={handleChangeEvent}
          renderValue={(selected: unknown): React.ReactNode => {
            // Don't show placeholder when dropdown is open or when value is selected
            if (!selected && !isOpen) {
              return <span style={{ color: '#999' }}>{placeholder}</span>;
            }
            if (!selected && isOpen) {
              return <span style={{ color: 'transparent' }}></span>;
            }
            const option = options.find(opt => opt.value === selected);
            return option ? option.label : String(selected);
          }}
          {...rest}
        >
          {options.map(option => (
            <MenuItem key={option.value} value={option.value}>
              {option.label}
            </MenuItem>
          ))}
        </Select>
        {(isError || helperText) && <FormHelperText>{isError ? <>{errorMessage}</> : helperText}</FormHelperText>}
      </FormControl>
    </Box>
  );
};

export default memo(SelectInput);
